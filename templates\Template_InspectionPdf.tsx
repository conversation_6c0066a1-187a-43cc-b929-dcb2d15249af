import { PropertyModel } from "@/app/models/Property";

interface TemplateProps extends PropertyModel {
    logoBase64?: string;
}

export default function Template_InspectionPdf(props: TemplateProps) {
    const { logoBase64, ...property } = props;
    return (
        <html>
            <head>
                <title>Property Inspection Report</title>
                <style>{`
                    body {
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        color: #333;
                    }
                    .logo {
                        text-align: center;
                        margin-bottom: 20px;
                    }
                    .logo img {
                        max-width: 200px;
                        height: auto;
                    }
                    .header {
                        text-align: center;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                        margin-bottom: 30px;
                    }
                    .header h1 {
                        margin: 0;
                        color: #2c3e50;
                        font-size: 28px;
                    }
                    .header h2 {
                        margin: 10px 0 0 0;
                        color: #7f8c8d;
                        font-weight: normal;
                        font-size: 18px;
                    }
                    .section-title {
                        background-color: #3498db;
                        color: white;
                        padding: 10px 15px;
                        margin: 20px 0 10px 0;
                        font-size: 18px;
                        font-weight: bold;
                    }
                    .details-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 15px;
                        margin-bottom: 20px;
                    }
                    .detail-item {
                        border: 1px solid #ddd;
                        padding: 10px;
                        background-color: #f9f9f9;
                    }
                    .detail-label {
                        font-weight: bold;
                        color: #2c3e50;
                        margin-bottom: 5px;
                    }
                    .detail-value {
                        color: #555;
                    }
                    .footer {
                        margin-top: 50px;
                        text-align: center;
                        border-top: 1px solid #ddd;
                        padding-top: 20px;
                        color: #7f8c8d;
                        font-size: 12px;
                    }
                    .generated-date {
                        margin-top: 30px;
                        text-align: right;
                        color: #7f8c8d;
                        font-size: 12px;
                    }
                `}</style>
            </head>
            <body>
                <div className="logo">
                    <img src={logoBase64 ? `data:image/webp;base64,${logoBase64}` : "/Main.webp"} alt="Company Logo" />
                </div>

                <div className="header">
                    <h1>Property Inspection Report</h1>
                    <h2>{property.Address || 'Property Address'}</h2>
                </div>

                <div className="section-title">Property Information</div>

                <div className="details-grid">
                    <div className="detail-item">
                        <div className="detail-label">Address</div>
                        <div className="detail-value">{property.Address || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Building Type</div>
                        <div className="detail-value">{property.BuildingTypeCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Company/Strata Title</div>
                        <div className="detail-value">{property.CompanyStrataTitleCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Number of Bedrooms</div>
                        <div className="detail-value">{property.NumBedroomsCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Orientation</div>
                        <div className="detail-value">{property.OrientationCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Storeys</div>
                        <div className="detail-value">{property.StoreysCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Furnished</div>
                        <div className="detail-value">{property.FurnishedCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Occupied</div>
                        <div className="detail-value">{property.OccupiedCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Floor</div>
                        <div className="detail-value">{property.FloorCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Roof</div>
                        <div className="detail-value">{property.RoofCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Walls</div>
                        <div className="detail-value">{property.WallsCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Weather</div>
                        <div className="detail-value">{property.WeatherCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Other Building Elements</div>
                        <div className="detail-value">{property.OtherBuildingElementsCode || 'Not specified'}</div>
                    </div>

                    <div className="detail-item">
                        <div className="detail-label">Other Timber Building Elements</div>
                        <div className="detail-value">{property.OtherTimberBldgElementsCode || 'Not specified'}</div>
                    </div>
                </div>

                {property.RoomsListJson && (
                    <>
                        <div className="section-title">Rooms</div>
                        <div className="detail-item">
                            <div className="detail-label">Room List</div>
                            <div className="detail-value">
                                {(() => {
                                    try {
                                        const rooms = JSON.parse(property.RoomsListJson);
                                        return Array.isArray(rooms) ? rooms.join(', ') : 'Invalid room data';
                                    } catch {
                                        return 'Invalid room data';
                                    }
                                })()}
                            </div>
                        </div>
                    </>
                )}

                <div className="generated-date">
                    Generated on: {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}
                </div>

                <div className="footer">
                    <p>This report was generated automatically by the Property Inspection System</p>
                    <p>Property ID: {property.Id}</p>
                </div>
            </body>
        </html>
    );
}
