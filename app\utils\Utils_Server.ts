import React from 'react';
import { renderComponentToHtml } from './renderComponent';

/**
 * Server-side utilities for rendering React components to HTML
 * This file contains utilities that should only be used on the server-side
 */
export class JC_Utils_Server {

    /**
     * Render a React component to HTML string (server-side only)
     * This is a wrapper around the renderComponentToHtml function for consistency
     * @param Component The React component to render
     * @param props The props to pass to the component
     * @returns HTML string with DOCTYPE
     */
    static renderComponentToHtml<P extends Record<string, any> = any>(
        Component: React.ComponentType<P>,
        props: P
    ): string {
        return renderComponentToHtml(Component, props);
    }
}

// Re-export the main function for convenience
export { renderComponentToHtml } from './renderComponent';
